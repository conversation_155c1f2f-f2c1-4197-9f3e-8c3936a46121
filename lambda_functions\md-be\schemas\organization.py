from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from uuid import UUID

# ========================
# Organization Schemas
# ========================

class OrganizationBase(BaseModel):
    name: Optional[str] = Field(None, description="Organization name")
    address1: Optional[str] = Field(None, description="Primary address")
    address2: Optional[str] = Field(None, description="Secondary address")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State")
    zip: Optional[str] = Field(None, description="ZIP code")
    phone: Optional[str] = Field(None, description="Phone number")
    annualRevenue: Optional[str] = Field(None, description="Annual revenue")
    industry: Optional[str] = Field(None, description="Industry")
    yearFounded: Optional[str] = Field(None, description="Year founded")
    businessProfileElementId: Optional[int] = Field(None, description="Business profile element ID")
    email: Optional[str] = Field(None, description="Email address")
    companySize: Optional[str] = Field(None, description="Company size")

class OrganizationCreate(BaseModel):
    name: str = Field(..., description="Organization name is required")
    address1: Optional[str] = Field(None, description="Primary address")
    address2: Optional[str] = Field(None, description="Secondary address")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State")
    zip: Optional[str] = Field(None, description="ZIP code")
    phone: Optional[str] = Field(None, description="Phone number")
    annualRevenue: Optional[str] = Field(None, description="Annual revenue")
    industry: Optional[str] = Field(None, description="Industry")
    yearFounded: Optional[str] = Field(None, description="Year founded")
    businessProfileElementId: Optional[int] = Field(None, description="Business profile element ID")
    email: Optional[str] = Field(None, description="Email address")
    companySize: Optional[str] = Field(None, description="Company size")

class OrganizationUpdate(OrganizationBase):
    pass

class OrganizationResponse(OrganizationBase):
    uuid: UUID = Field(..., description="Organization UUID")
    createdBy: Optional[UUID] = Field(None, description="User who created the organization")
    updatedBy: Optional[UUID] = Field(None, description="User who updated the organization")
    dateCreated: datetime = Field(..., description="Creation timestamp")
    dateUpdated: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True

# ========================
# Organization Verified Data Schemas
# ========================

class OrganizationVerifiedDataBase(BaseModel):
    name: Optional[str] = Field(None, description="Verified organization name")
    address: Optional[str] = Field(None, description="Verified address")
    city: Optional[str] = Field(None, description="Verified city")
    state: Optional[str] = Field(None, description="Verified state")
    zip: Optional[str] = Field(None, description="Verified ZIP code")
    phone: Optional[str] = Field(None, description="Verified phone number")
    ein: Optional[str] = Field(None, description="Employer Identification Number")
    industry: Optional[str] = Field(None, description="Verified industry")
    foundingYear: Optional[int] = Field(None, description="Verified founding year")
    verificationStatus: str = Field(..., description="Verification status")
    verificationType: str = Field(..., description="Verification type")

class OrganizationVerifiedDataCreate(OrganizationVerifiedDataBase):
    organizationUuid: UUID = Field(..., description="Organization UUID")

class OrganizationVerifiedDataUpdate(BaseModel):
    name: Optional[str] = Field(None, description="Verified organization name")
    address: Optional[str] = Field(None, description="Verified address")
    city: Optional[str] = Field(None, description="Verified city")
    state: Optional[str] = Field(None, description="Verified state")
    zip: Optional[str] = Field(None, description="Verified ZIP code")
    phone: Optional[str] = Field(None, description="Verified phone number")
    ein: Optional[str] = Field(None, description="Employer Identification Number")
    industry: Optional[str] = Field(None, description="Verified industry")
    foundingYear: Optional[int] = Field(None, description="Verified founding year")
    verificationStatus: Optional[str] = Field(None, description="Verification status")
    verificationType: Optional[str] = Field(None, description="Verification type")

class OrganizationVerifiedDataResponse(OrganizationVerifiedDataBase):
    uuid: UUID = Field(..., description="Verified data UUID")
    organizationUuid: UUID = Field(..., description="Organization UUID")
    createdBy: Optional[UUID] = Field(None, description="User who created the verified data")
    updatedBy: Optional[UUID] = Field(None, description="User who updated the verified data")
    dateCreated: datetime = Field(..., description="Creation timestamp")
    dateUpdated: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True

# ========================
# Member Organization Relations Schemas
# ========================

class MemberOrganizationRelationCreate(BaseModel):
    memberUuid: UUID = Field(..., description="Member UUID")
    organizationUuid: UUID = Field(..., description="Organization UUID")

class MemberOrganizationRelationResponse(BaseModel):
    memberUuid: UUID = Field(..., description="Member UUID")
    organizationUuid: UUID = Field(..., description="Organization UUID")
    createdBy: Optional[UUID] = Field(None, description="User who created the relation")
    dateCreated: datetime = Field(..., description="Creation timestamp")

    class Config:
        from_attributes = True

# ========================
# Member Awards Schemas
# ========================

class MemberAwardBase(BaseModel):
    memberUuid: UUID = Field(..., description="Member UUID")
    organizationUuid: UUID = Field(..., description="Organization UUID")
    awardListingElementId: int = Field(..., description="Award listing element ID")
    openWaterUserId: Optional[int] = Field(None, description="OpenWater user ID")
    openWaterApplicationId: Optional[int] = Field(None, description="OpenWater application ID")
    status: str = Field(..., description="Award status")
    progress: Optional[int] = Field(None, description="Application progress")
    categories: Optional[dict] = Field(None, description="Award categories")
    isDisqualified: Optional[bool] = Field(False, description="Is disqualified")
    isPreviousWinner: Optional[bool] = Field(False, description="Is previous winner")
    isQualified: Optional[bool] = Field(False, description="Is qualified")
    isJudged: Optional[bool] = Field(None, description="Is judged")
    isPaid: Optional[bool] = Field(None, description="Is paid")
    isWinner: Optional[bool] = Field(None, description="Is winner")
    winnerTypes: Optional[str] = Field(None, description="Winner types")
    applicationLink: Optional[str] = Field(None, description="Application link")
    startedDate: Optional[datetime] = Field(None, description="Started date")
    submittedDate: Optional[datetime] = Field(None, description="Submitted date")

class MemberAwardCreate(MemberAwardBase):
    pass

class MemberAwardUpdate(BaseModel):
    awardListingElementId: Optional[int] = Field(None, description="Award listing element ID")
    openWaterUserId: Optional[int] = Field(None, description="OpenWater user ID")
    openWaterApplicationId: Optional[int] = Field(None, description="OpenWater application ID")
    status: Optional[str] = Field(None, description="Award status")
    progress: Optional[int] = Field(None, description="Application progress")
    categories: Optional[dict] = Field(None, description="Award categories")
    isDisqualified: Optional[bool] = Field(None, description="Is disqualified")
    isPreviousWinner: Optional[bool] = Field(None, description="Is previous winner")
    isQualified: Optional[bool] = Field(None, description="Is qualified")
    isJudged: Optional[bool] = Field(None, description="Is judged")
    isPaid: Optional[bool] = Field(None, description="Is paid")
    isWinner: Optional[bool] = Field(None, description="Is winner")
    winnerTypes: Optional[str] = Field(None, description="Winner types")
    applicationLink: Optional[str] = Field(None, description="Application link")
    startedDate: Optional[datetime] = Field(None, description="Started date")
    submittedDate: Optional[datetime] = Field(None, description="Submitted date")

class MemberAwardResponse(MemberAwardBase):
    uuid: UUID = Field(..., description="Award UUID")
    createdBy: Optional[str] = Field(None, description="User who created the award")
    updatedBy: Optional[str] = Field(None, description="User who updated the award")
    dateCreated: datetime = Field(..., description="Creation timestamp")
    dateUpdated: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True

# ========================
# Enhanced Response Schemas
# ========================

class OrganizationWithVerifiedData(OrganizationResponse):
    verifiedData: Optional[OrganizationVerifiedDataResponse] = Field(None, description="Verified data for the organization")

class OrganizationWithRelations(OrganizationResponse):
    memberRelations: List[MemberOrganizationRelationResponse] = Field([], description="Member relations")
    awards: List[MemberAwardResponse] = Field([], description="Awards")

# ========================
# Pagination Schema
# ========================

class PaginationInfo(BaseModel):
    totalCount: int = Field(..., description="Total number of items")
    currentPage: int = Field(..., description="Current page number")
    pageSize: int = Field(..., description="Number of items per page")
    totalPages: int = Field(..., description="Total number of pages")
    hasNext: bool = Field(..., description="Whether there is a next page")
    hasPrevious: bool = Field(..., description="Whether there is a previous page")
