"""
Utility functions for standardizing API response formats across the codebase.

This module provides consistent response structures for common HTTP status codes,
ensuring all API endpoints return responses in a uniform format.
"""

from typing import Any, Dict, Optional
from fastapi import status


def _create_response(status_code: int, success: bool, message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Internal helper function to create standardized response structure.
    
    Args:
        status_code: HTTP status code
        success: Boolean indicating if the operation was successful
        message: Human-readable message describing the result
        data: Optional data payload
        
    Returns:
        Dictionary with standardized response structure
    """
    response = {
        "status_code": status_code,
        "success": success,
        "message": message
    }
    
    # Only include data field if data is provided
    if data is not None:
        response.update(data if isinstance(data, dict) else {"data": data})
    
    return response


def success_response(message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create a successful response (200 OK).
    
    Args:
        message: Success message
        data: Optional data payload
        
    Returns:
        Standardized success response
        
    Example:
        success_response("User retrieved successfully", {"user": user_data})
        # Returns: {"status_code": 200, "success": True, "message": "...", "user": user_data}
    """
    return _create_response(status.HTTP_200_OK, True, message, data)


def created_response(message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create a resource created response (201 Created).
    
    Args:
        message: Creation success message
        data: Optional data payload
        
    Returns:
        Standardized created response
        
    Example:
        created_response("Admin created successfully", {"admin": admin_data})
        # Returns: {"status_code": 201, "success": True, "message": "...", "admin": admin_data}
    """
    return _create_response(status.HTTP_201_CREATED, True, message, data)


def bad_request_response(message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create a bad request response (400 Bad Request).
    
    Args:
        message: Error message
        data: Optional error details
        
    Returns:
        Standardized bad request response
    """
    return _create_response(status.HTTP_400_BAD_REQUEST, False, message, data)


def unauthorized_response(message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create an unauthorized response (401 Unauthorized).
    
    Args:
        message: Error message
        data: Optional error details
        
    Returns:
        Standardized unauthorized response
    """
    return _create_response(status.HTTP_401_UNAUTHORIZED, False, message, data)


def forbidden_response(message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create a forbidden response (403 Forbidden).
    
    Args:
        message: Error message
        data: Optional error details
        
    Returns:
        Standardized forbidden response
    """
    return _create_response(status.HTTP_403_FORBIDDEN, False, message, data)


def not_found_response(message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create a not found response (404 Not Found).
    
    Args:
        message: Error message
        data: Optional error details
        
    Returns:
        Standardized not found response
    """
    return _create_response(status.HTTP_404_NOT_FOUND, False, message, data)


def conflict_response(message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create a conflict response (409 Conflict).
    
    Args:
        message: Error message
        data: Optional error details
        
    Returns:
        Standardized conflict response
    """
    return _create_response(status.HTTP_409_CONFLICT, False, message, data)


def unprocessable_entity_response(message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create an unprocessable entity response (422 Unprocessable Entity).
    
    Args:
        message: Error message
        data: Optional error details
        
    Returns:
        Standardized unprocessable entity response
    """
    return _create_response(status.HTTP_422_UNPROCESSABLE_ENTITY, False, message, data)


def internal_server_error_response(message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create an internal server error response (500 Internal Server Error).
    
    Args:
        message: Error message
        data: Optional error details
        
    Returns:
        Standardized internal server error response
    """
    return _create_response(status.HTTP_500_INTERNAL_SERVER_ERROR, False, message, data)


def error_response(status_code: int, message: str, data: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create a generic error response with custom status code.
    
    Args:
        status_code: HTTP status code
        message: Error message
        data: Optional error details
        
    Returns:
        Standardized error response
        
    Example:
        error_response(418, "I'm a teapot", {"reason": "coffee_not_supported"})
    """
    return _create_response(status_code, False, message, data)


def no_content_response() -> Dict[str, Any]:
    """
    Create a no content response (204 No Content).
    
    Returns:
        Standardized no content response
    """
    return _create_response(status.HTTP_204_NO_CONTENT, True, "Operation completed successfully")
