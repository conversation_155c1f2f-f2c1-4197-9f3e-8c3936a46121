# AWS Secrets Manager Authentication Module

import boto3
import json
import logging
from typing import Dict, Any
from botocore.exceptions import ClientError, NoCredentialsError, BotoCoreError

# Configure logging
logger = logging.getLogger(__name__)

class SecretsManagerAuth:
    # Manages AWS Secrets Manager authentication for database connections

    def __init__(self, secret_name: str, region: str):
        self.secret_name = secret_name
        self.region = region
        
        # Secrets Manager client
        self._secrets_client = None
        
        logger.info(f"Secrets Manager Auth initialized for secret '{secret_name}' in region '{region}'")
    
    def _get_secrets_client(self):
        # Get or create Secrets Manager client using AWS credentials chain
        if self._secrets_client is None:
            try:
                # Use AWS credentials chain (IAM roles, credentials file, etc.)
                self._secrets_client = boto3.client(
                    'secretsmanager',
                    region_name=self.region,
                    
                )
                logger.debug("Secrets Manager client created using AWS credentials chain")
            except Exception as e:
                logger.error(f"Failed to create Secrets Manager client: {str(e)}")
                raise
        return self._secrets_client
    
    def get_database_credentials(self) -> Dict[str, str]:
        # Retrieve database credentials and configuration from AWS Secrets Manager
        try:
            client = self._get_secrets_client()

            logger.debug(f"Retrieving database configuration from secret '{self.secret_name}'")

            # Get the secret value
            response = client.get_secret_value(SecretId=self.secret_name)

            # Parse the secret string (should be JSON)
            secret_string = response['SecretString']
            credentials = json.loads(secret_string)

            # Validate required fields
            required_fields = ['username', 'password', 'host', 'port', 'dbname']
            for field in required_fields:
                if field not in credentials:
                    raise Exception(f"Secret does not contain '{field}' field")

            logger.info("Database configuration retrieved successfully from Secrets Manager")

            return {
                'username': credentials['username'],
                'password': credentials['password'],
                'host': credentials['host'],
                'port': credentials['port'],
                'dbname': credentials['dbname']
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse secret as JSON: {str(e)}")
            raise Exception(f"Secret contains invalid JSON: {str(e)}")
        except NoCredentialsError as e:
            logger.error("AWS credentials not found or invalid")
            raise Exception(f"AWS credentials not found: {str(e)}")
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"AWS ClientError ({error_code}): {error_message}")
            
            # Provide more specific error messages for common issues
            if error_code == 'ResourceNotFoundException':
                raise Exception(f"Secret '{self.secret_name}' not found in region '{self.region}'")
            elif error_code == 'AccessDeniedException':
                raise Exception(f"Access denied to secret '{self.secret_name}'. Check IAM permissions.")
            elif error_code == 'InvalidRequestException':
                raise Exception(f"Invalid request for secret '{self.secret_name}': {error_message}")
            else:
                raise Exception(f"AWS Secrets Manager error ({error_code}): {error_message}")
        except BotoCoreError as e:
            logger.error(f"AWS BotoCoreError: {str(e)}")
            raise Exception(f"AWS service error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error retrieving database credentials: {str(e)}")
            raise Exception(f"Failed to retrieve database credentials: {str(e)}")
    
    def get_password(self) -> str:
        # Get just the password from AWS Secrets Manager
        credentials = self.get_database_credentials()
        return credentials['password']

    def get_username(self) -> str:
        # Get just the username from AWS Secrets Manager
        credentials = self.get_database_credentials()
        return credentials['username']

    def get_host(self) -> str:
        # Get just the host from AWS Secrets Manager
        credentials = self.get_database_credentials()
        return credentials['host']

    def get_port(self) -> int:
        # Get just the port from AWS Secrets Manager
        credentials = self.get_database_credentials()
        return int(credentials['port'])

    def get_dbname(self) -> str:
        # Get just the database name from AWS Secrets Manager
        credentials = self.get_database_credentials()
        return credentials['dbname']
    
    def test_secret_access(self) -> Dict[str, Any]:
        # Test access to the secret without retrieving sensitive data
        try:
            # Try to retrieve credentials
            credentials = self.get_database_credentials()
            
            # Return success without exposing sensitive data
            return {
                "status": "success",
                "secret_name": self.secret_name,
                "region": self.region,
                "has_username": bool(credentials.get('username')),
                "has_password": bool(credentials.get('password')),
                "has_host": bool(credentials.get('host')),
                "has_port": bool(credentials.get('port')),
                "has_dbname": bool(credentials.get('dbname')),
                "message": "Secret access test successful"
            }
            
        except Exception as e:
            logger.error(f"Secret access test failed: {str(e)}")
            return {
                "status": "error",
                "secret_name": self.secret_name,
                "region": self.region,
                "error_message": str(e),
                "message": "Secret access test failed"
            }
    
    def get_secret_info(self) -> Dict[str, Any]:
        # Get information about the secret configuration
        return {
            "secret_name": self.secret_name,
            "region": self.region,
            "client_created": self._secrets_client is not None
        }


# Global secrets manager auth instance
_secrets_auth = None

def get_secrets_auth() -> SecretsManagerAuth:
    # Get the global Secrets Manager auth instance
    global _secrets_auth
    if _secrets_auth is None:
        raise RuntimeError("Secrets Manager Auth not initialized. Call initialize_secrets_auth() first.")
    return _secrets_auth

def initialize_secrets_auth(secret_name: str, region: str) -> SecretsManagerAuth:
    # Initialize the global Secrets Manager auth
    global _secrets_auth
    _secrets_auth = SecretsManagerAuth(secret_name, region)
    logger.info("Global Secrets Manager Auth initialized")
    return _secrets_auth
