
from schemas.member import CoMemberC<PERSON>, LoginRequest
from services import member_authentication_service
from sqlalchemy.orm import Session

class MemberAuthenticationController:
    def create_member(self, member: CoMemberCreate, admin_user_payload: dict, db: Session):
        user_id_and_token = member_authentication_service.register_user_in_auth0(member.loginEmail, member.password, admin_user_payload)
        return member_authentication_service.create_member(member, admin_user_payload, user_id_and_token, db)

    def login(self, payload: LoginRequest, db: Session):
        return member_authentication_service.login(payload.loginEmail, payload.password, db)

    def login_redirect(self, provider: str):
        return member_authentication_service.login_redirect(provider)

    def callback(self, code: str, db: Session):
        return member_authentication_service.callback(code, db)