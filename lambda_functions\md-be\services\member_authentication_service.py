import logging
from venv import create
from fastapi import HTTPException, status
import requests.compat
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from auth0_manage_api.auth0_manage import get_management_token
from models.member import CoMember
from models.admin import AdminModel
from schemas.member import Co<PERSON><PERSON>berCreate
from auth0.management import Auth0
import requests
from jose import jwt
from config import settings
from utils import auth_admin
from utils.response_utils import created_response, success_response

def create_member(member: CoMemberCreate, admin_user_payload: dict, user_id_and_token: dict, db: Session):
    try:
        # Manually check required fields (optional if enforced by schema, but extra safe)
        required_fields = {
            "loginEmail": member.loginEmail,
            "password": member.password,
            "membershipTier": member.membershipTier
        }

        missing = [field for field, value in required_fields.items() if not value]
        if missing:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "message": f"Missing required field(s): {', '.join(missing)}",
                    "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY
                }
            )

        # Check if member already exists
        if db.query(CoMember).filter_by(auth0Id=user_id_and_token["auth0id"]).first():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "message": "User already registered.",
                    "status_code": status.HTTP_409_CONFLICT
                }
            )

        new_member = CoMember(
            auth0Id=user_id_and_token["auth0id"],
            loginEmail=member.loginEmail,
            customerIoId=member.customerIoId,
            openWaterId=member.openWaterId,
            firstName=member.firstName,
            lastName=member.lastName,
            loginEmailVerified=member.loginEmailVerified,
            identityType=member.identityType,
            personalBusinessEmail=member.personalBusinessEmail,
            phone=member.phone,
            professionalTitle=member.professionalTitle,
            membershipTier=member.membershipTier,
            communityStatus=member.communityStatus,
        )
        
        # Set created/updated by fields using UUIDs
        if admin_user_payload.get("sub"):
            new_member.createdByAdmin = admin_user_payload["sub"]
            new_member.updatedByAdmin = admin_user_payload["sub"]

        db.add(new_member)
        db.commit()
        db.refresh(new_member)

        # For self-registration, set the created/updated by fields to the new member's own UUID
        if not admin_user_payload.get("sub"):
            new_member.createdByMember = new_member.uuid
            new_member.updatedByMember = new_member.uuid
            db.commit()
            db.refresh(new_member)

        return created_response(
            "Member created successfully",
            {
                "user": {
                    "loginEmail": new_member.loginEmail,
                    "firstName": new_member.firstName,
                    "lastName": new_member.lastName,
                    "membershipTier": new_member.membershipTier,
                    "communityStatus": new_member.communityStatus,
                    "customerIoId": new_member.customerIoId,
                    "openWaterId": new_member.openWaterId,
                    "identityType": new_member.identityType,
                    "personalBusinessEmail": new_member.personalBusinessEmail,
                    "phone": new_member.phone,
                    "professionalTitle": new_member.professionalTitle,
                    "loginEmailVerified": new_member.loginEmailVerified,
                    "hasSeenFirstLoginMessage": new_member.hasSeenFirstLoginMessage
                },
                "token": user_id_and_token["token"]
            }
        )

    except HTTPException as http_exc:
        raise http_exc

    except SQLAlchemyError as db_err:
        db.rollback()
        logging.exception("Database error during registration:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "A database error occurred. Please try again later.",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

    except Exception as e:
        logging.exception("Unexpected error during registration:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during registration.",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

def login(loginEmail: str, password: str, db: Session):
    url = f"https://{settings.DOMAIN}/oauth/token"

    payload = {
        "grant_type": "http://auth0.com/oauth/grant-type/password-realm",
        "username": loginEmail,
        "password": password,
        "audience": settings.AUDIENCE,
        "client_id": settings.CLIENT_ID,
        "client_secret": settings.CLIENT_SECRET,
        "scope": "openid profile email",
        "realm": "Username-Password-Authentication"
    }

    response = requests.post(url, json=payload)
    
    if response.status_code != 200:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                    "message": "Login failed due to invalid credentials",
                    "status_code": status.HTTP_401_UNAUTHORIZED
                }
        )

    access_token = response.json().get("access_token")

    if not access_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "Login failed: Access token not received.",
                "status_code": status.HTTP_401_UNAUTHORIZED
            }
        )
        
    # Decode the token to recheck in db
    try:
        decoded = jwt.get_unverified_claims(access_token)
        auth0id = decoded.get("sub")
        
        if not auth0id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Invalid token: 'sub' (Auth0 user ID) is missing.",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        existing_user = db.query(CoMember).filter_by(auth0Id=auth0id).first()

        if existing_user:
            return success_response(
                "Login successful",
                {
                    "member_info": {
                        "firstName" : existing_user.firstName,
                        "lastName" : existing_user.lastName,
                        "loginEmail" : existing_user.loginEmail,
                        "phone" : existing_user.phone,
                    },
                    "token": access_token
                }
            )

        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "message": "User not found in the database. Please register first.",
                    "status_code": status.HTTP_401_UNAUTHORIZED
                }
            )

    except HTTPException as http_exc:
        raise http_exc

    except Exception as e:
        logging.exception("Unexpected error during login:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": f"Internal server error: {str(e)}",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
        
def login_redirect(provider: str):
    base_url = f"https://{settings.DOMAIN}/authorize"
    params = {
        "client_id": settings.CLIENT_ID,
        "response_type": "code",
        "redirect_uri": settings.REDIRECT_URI,
        "scope": settings.SCOPE,
        "audience": settings.AUDIENCE,
    }

    if provider.lower() == 'google':
        params["connection"] = "google-oauth2"
    elif provider.lower() == 'apple':
        params["connection"] = "apple"
    elif provider.lower() == 'linkedin':
        params["connection"] = "linkedin"

    url = f"{base_url}?{requests.compat.urlencode(params)}"
    return {
        "status_code": status.HTTP_302_FOUND,
        "success": True,
        "url": url
    }
        
def callback(code: str, db: Session):
    if not code:
        raise HTTPException(status_code=400, detail="Missing code")

    token_url = f"https://{settings.DOMAIN}/oauth/token"

    payload = {
        "grant_type": "authorization_code",
        "client_id": settings.CLIENT_ID,
        "client_secret": settings.CLIENT_SECRET,
        "code": code,
        "redirect_uri": settings.REDIRECT_URI
    }

    headers = { "Content-Type": "application/json" }
    

    response = requests.post(token_url, json=payload, headers=headers)
    data = response.json()

    if "id_token" not in data:
        raise HTTPException(status_code=400, detail=f"Auth failed: {data}")

    return success_response(
        "Authentication successful",
        {
            "token": data["access_token"],
            "id_token": data["id_token"]
        }
    )

def register_user_in_auth0(email: str, password: str, admin_user_payload: dict):
    domain = settings.DOMAIN
    token = get_management_token()
    
    if not domain or not token:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Auth0 domain or token is not configured properly."
        )
    auth0 = Auth0(domain, token)

    try:
        new_user = auth0.users.create({
            "email": email,
            "password": password,
            "connection": "Username-Password-Authentication"
        })
        
        if admin_user_payload.get("sub"):
            auth0.users.update(
                id=new_user["user_id"],
                body={"email_verified": True}
            )
        else:
            pass

    except Exception as e:
        raise HTTPException(status_code=409, detail=str(e))

    return {
        "auth0id": new_user["user_id"],
        "token": token
    }