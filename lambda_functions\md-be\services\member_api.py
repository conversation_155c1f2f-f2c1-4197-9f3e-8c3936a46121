from auth0_manage_api.auth0_manage import get_management_token
from auth0.management import Auth0
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from models.member import CoMember
from schemas.member import CoMemberCreate, CoMemberUpdate, CoMemberResponse
from typing import List, Optional
from config import settings
from utils.response_utils import success_response, not_found_response

def get_all_members(db: Session, page: int, pageSize: int):
    # Get total count
    total_count = db.query(CoMember).count()
    
    # Get paginated results
    members: List[CoMember] = db.query(CoMember).offset((page - 1) * pageSize).limit(pageSize).all()
    
    if not members:
        return success_response(
            "No members found",
            {
                "members": [],
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": page,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": page * pageSize < total_count,
                    "hasPrevious": page > 1
                }
            }
        )
    
    return success_response(
        "Members retrieved successfully",
        {
            "members": [CoMemberResponse.model_validate(member) for member in members],
            "pagination": {
                "totalCount": total_count,
                "currentPage": page,
                "pageSize": pageSize,
                "totalPages": (total_count + pageSize - 1) // pageSize,
                "hasNext": page * pageSize < total_count,
                "hasPrevious": page > 1
            }
        }
    )

def get_member_by_auth0id(auth0id: str, db: Session):
    member = db.query(CoMember).filter_by(auth0Id=auth0id).first()
    if not member:
        raise HTTPException(status_code=404, detail="No member found")
    return success_response(
        "Member retrieved successfully",
        {"member": CoMemberResponse.model_validate(member)}
    )

def get_member_by_uuid(uuid: str, db: Session):
    member = db.query(CoMember).filter_by(uuid=uuid).first()
    if not member:
        raise HTTPException(status_code=404, detail="No member found")
    return success_response(
        "Member retrieved successfully",
        {"member": CoMemberResponse.model_validate(member)}
    )

def update_member_by_uuid(uuid: str, member: CoMemberUpdate, db: Session, admin_user_payload: Optional[dict] = None):
    existing_member = db.query(CoMember).filter_by(uuid=uuid).first()
    if not existing_member:
        raise HTTPException(status_code=404, detail="No member found")
    
    # Update user in Auth0 only if auth0Id exists (email and password must be updated separately)
    if existing_member.auth0Id is not None:
        domain = settings.DOMAIN
        token = get_management_token()

        if not domain or not token:
            raise HTTPException(
                status_code=500,
                detail="Auth0 configuration error: domain or token not available"
            )

        auth0 = Auth0(domain, token)

        try:
            # Update password first if provided
            if member.password:
                auth0.users.update(
                    id=str(existing_member.auth0Id),
                    body={
                        "password": member.password
                    }
                )

            # Update email and name second (only if email has changed)
            if member.loginEmail and member.loginEmail != existing_member.loginEmail:
                auth0.users.update(
                    id=str(existing_member.auth0Id),
                    body={
                        "email": member.loginEmail,
                        "name": member.loginEmail,
                        "email_verified": False  # Email needs to be re-verified after change
                    }
                )

        except Exception as auth0_error:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to update user in Auth0: {str(auth0_error)}"
            )

    # Update member fields in database
    for key, value in member.model_dump(exclude_unset=True).items():
        if hasattr(existing_member, key):
            setattr(existing_member, key, value)
    
    # Set updated by fields if admin_user_payload is provided
    if admin_user_payload and admin_user_payload.get("sub"):
        existing_member.updatedByAdmin = admin_user_payload["sub"]

    db.commit()
    db.refresh(existing_member)

    # Set the updated by fields to the new member's own UUID (if not already set)
    if not admin_user_payload or not admin_user_payload.get("sub"):
        existing_member.updatedByMember = existing_member.uuid
        db.commit()
        db.refresh(existing_member)

    return success_response(
        "Member updated successfully",
        {"member": CoMemberResponse.model_validate(existing_member)}
    )

def get_member_by_email(email: str, db: Session):
    member = db.query(CoMember).filter_by(loginEmail=email).first()
    if not member:
        raise HTTPException(status_code=404, detail="No member found")
    return success_response(
        "Member retrieved successfully",
        {"member": CoMemberResponse.model_validate(member)}
    )

def delete_member(uuid: str, db: Session, admin_user_payload: Optional[dict] = None):
    member = db.query(CoMember).filter_by(uuid=uuid).first()
    if not member:
        raise HTTPException(status_code=404, detail="No member found")

    # Delete user from Auth0 with proper error handling
    domain = settings.DOMAIN
    token = get_management_token()

    if domain and token and member.auth0Id is not None:
        try:
            auth0 = Auth0(domain, token)
            auth0.users.delete(id=str(member.auth0Id))
        except Exception as auth0_error:
            # Log the error but don't fail the entire operation
            print(f"Warning: Failed to delete user from Auth0: {str(auth0_error)}")
            # Continue with database deletion

    # Delete from database
    db.delete(member)
    db.commit()

    return success_response("Member deleted successfully")