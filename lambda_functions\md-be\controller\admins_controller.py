from schemas.admin import Admin, Admin_cognito_create, AdminUpdate, AdminResponse
from services import admin_api
from sqlalchemy.orm import Session

class AdminController:
    def create_admin_cognito(self, token: str, new_admin: Admin_cognito_create, db: Session):
        return admin_api.create_admin_cognito(token, new_admin, db)

    def get_admin_list(self, db: Session, page: int, pageSize: int):
        return admin_api.get_admin_list(db, page=page, pageSize=pageSize)

    def get_admin_by_uuid(self, admin_uuid: str, db: Session):
        return admin_api.get_admin_by_uuid(admin_uuid, db)
    
    def get_admin_user(self, admin_user_payload: dict, db: Session):
        return admin_api.get_admin_user(admin_user_payload, db)
    
    def update_admin(self, uuid: str, admin: AdminUpdate, db: Session, admin_user_payload: dict):
        return admin_api.update_admin(uuid, admin, db, admin_user_payload)

    def delete_admin(self, uuid: str, db: Session, admin_user_payload: dict):
        return admin_api.delete_admin(uuid, db, admin_user_payload)
